import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { AppSettings, AppState, AuthInfo, Config, ConfigFormData, ConfigType, Group, MonitoringStatus, ProxyServerConfig, SUIConfig, ThreeXUIConfig, XUIConfig } from './types';

// 存储键
const STORAGE_KEYS = {
  CONFIGS: 'app_configs',
  GROUPS: 'app_groups',
  SETTINGS: 'app_settings',
  SELECTED_GROUP: 'selected_group_id',
  MONITORING_STATUS: 'monitoring_status',
};

// 默认设置
const DEFAULT_SETTINGS: AppSettings = {
  theme: 'system',
  language: 'en',
  isPro: false,
};

// 默认分组 - 名称将通过翻译系统动态显示
const DEFAULT_GROUPS: Group[] = [
  {
    id: 'all',
    name: 'All', // 默认英文名称，实际显示时会通过getGroupDisplayName函数处理
    order: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

interface AppStore extends AppState {
  // 配置相关操作
  addConfig: (formData: ConfigFormData, type: ConfigType) => Promise<void>;
  updateConfig: (id: string, updates: Partial<Config>, newGroupIds?: string[]) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;

  // 分组相关操作
  addGroup: (name: string) => Promise<void>;
  updateGroup: (id: string, updates: Partial<Group>) => Promise<void>;
  deleteGroup: (id: string) => Promise<void>;
  reorderGroups: (groups: Group[]) => Promise<void>;

  // 选择分组
  setSelectedGroup: (groupId: string | null) => Promise<void>;

  // 设置相关操作
  updateSettings: (updates: Partial<AppSettings>) => Promise<void>;

  // 认证相关操作
  setAuthInfo: (configId: string, authInfo: AuthInfo) => void;
  getAuthInfo: (configId: string) => AuthInfo | undefined;
  clearAuthInfo: (configId: string) => void;
  isAuthValid: (configId: string) => boolean;

  // 监控相关操作
  setMonitoringStatus: (configId: string, status: MonitoringStatus) => void;
  getMonitoringStatus: (configId: string) => MonitoringStatus | undefined;
  clearMonitoringStatus: (configId: string) => void;
  persistMonitoringStatus: () => Promise<void>;

  // 中转服务器相关操作
  setProxyServer: (proxyServer: ProxyServerConfig | null) => void;
  getProxyServer: () => ProxyServerConfig | null;

  // 服务器配置相关操作
  setServerConfig: (configId: string, serverConfig: any) => void;
  getServerConfig: (configId: string) => any | undefined;
  clearServerConfig: (configId: string) => void;

  // 数据加载
  loadData: () => Promise<void>;

  // 获取过滤后的配置
  getFilteredConfigs: () => Config[];

  // 清空所有数据
  clearAllData: () => Promise<void>;
}

export const useAppStore = create<AppStore>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    configs: [],
    groups: DEFAULT_GROUPS,
    settings: DEFAULT_SETTINGS,
    selectedGroupId: 'all',
    isLoading: true,
    authInfo: {},
    monitoringStatus: {},
    proxyServer: null,
    serverConfig: {},

    // 配置相关操作
    addConfig: async (formData: ConfigFormData, type: ConfigType) => {
      // 获取用户选择的分组ID
      const selectedGroupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);

      // 获取"全部"分组的ID
      const allGroupId = get().groups.find(g => g.id === 'all')?.id;
      if (!allGroupId) {
        throw new Error('All group not found');
      }

      // 配置必须添加到"全部"分组，同时添加到用户选择的其他分组
      // 使用Set去重，避免重复的"全部"分组
      const groupIds = Array.from(new Set([allGroupId, ...selectedGroupIds]));

      const configId = await Crypto.randomUUID();
      const timestamp = new Date().toISOString();

      let newConfig: Config;

      if (type === 's-ui') {
        newConfig = {
          id: configId,
          name: formData.name,
          type: 's-ui',
          url: formData.url,
          groupIds: groupIds, // 直接存储分组ID数组
          createdAt: timestamp,
          updatedAt: timestamp,
          api: formData.api!,
          protocol: formData.protocol,
          cert: formData.cert,
          certFingerprints: formData.certFingerprints || []
        } as SUIConfig;
      } else {
        newConfig = {
          id: configId,
          name: formData.name,
          type,
          url: formData.url,
          groupIds: groupIds, // 直接存储分组ID数组
          createdAt: timestamp,
          updatedAt: timestamp,
          username: formData.username!,
          password: formData.password!,
          protocol: formData.protocol,
          cert: formData.cert,
          certFingerprints: formData.certFingerprints || []
        } as XUIConfig | ThreeXUIConfig;
      }

      set((state) => ({
        configs: [...state.configs, newConfig],
      }));
    },

    updateConfig: async (id: string, updates: Partial<Config>, newGroupIds?: string[]) => {
      const { groups } = get();

      // 如果提供了新的分组ID列表，需要处理分组关系
      if (newGroupIds !== undefined) {
        // 获取"全部"分组的ID
        const allGroupId = groups.find(g => g.id === 'all')?.id;
        if (!allGroupId) {
          throw new Error('All group not found');
        }

        // 确保包含"全部"分组，并去重
        const finalGroupIds = Array.from(new Set([allGroupId, ...newGroupIds]));

        // 更新配置，包括分组信息
        set((state) => ({
          configs: state.configs.map((config) =>
            config.id === id
              ? { ...config, ...updates, groupIds: finalGroupIds, updatedAt: new Date().toISOString() } as Config
              : config
          ),
        }));
      } else {
        // 如果没有提供分组ID，只更新配置内容，保持原有分组
        set((state) => ({
          configs: state.configs.map((config) =>
            config.id === id
              ? { ...config, ...updates, updatedAt: new Date().toISOString() } as Config
              : config
          ),
        }));
      }
    },

    deleteConfig: async (id: string) => {
      set((state) => ({
        configs: state.configs.filter((config) => config.id !== id),
      }));
    },

    // 分组相关操作
    addGroup: async (name: string) => {
      const { groups } = get();
      const maxOrder = Math.max(...groups.map(g => g.order));
      
      const newGroup: Group = {
        id: await Crypto.randomUUID(),
        name,
        order: maxOrder + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      set((state) => ({
        groups: [...state.groups, newGroup],
      }));
    },

    updateGroup: async (id: string, updates: Partial<Group>) => {
      set((state) => ({
        groups: state.groups.map((group) =>
          group.id === id
            ? { ...group, ...updates, updatedAt: new Date().toISOString() }
            : group
        ),
      }));
    },

    deleteGroup: async (id: string) => {
      const { configs } = get();

      // 从所有配置的分组数组中移除被删除的分组ID
      const updatedConfigs = configs.map((config) => {
        if (config.groupIds.includes(id)) {
          const newGroupIds = config.groupIds.filter(groupId => groupId !== id);
          // 确保至少保留"全部"分组
          if (newGroupIds.length === 0 || !newGroupIds.includes('all')) {
            newGroupIds.push('all');
          }
          return { ...config, groupIds: Array.from(new Set(newGroupIds)), updatedAt: new Date().toISOString() };
        }
        return config;
      });

      set((state) => ({
        groups: state.groups.filter((group) => group.id !== id),
        configs: updatedConfigs,
        selectedGroupId: state.selectedGroupId === id ? 'all' : state.selectedGroupId,
      }));
    },

    reorderGroups: async (groups: Group[]) => {
      set({ groups });
    },

    // 选择分组
    setSelectedGroup: async (groupId: string | null) => {
      set({ selectedGroupId: groupId });
    },

    // 设置相关操作
    updateSettings: async (updates: Partial<AppSettings>) => {
      set((state) => ({
        settings: { ...state.settings, ...updates },
      }));
    },

    // 认证相关操作
    setAuthInfo: (configId: string, authInfo: AuthInfo) => {
      set((state) => ({
        authInfo: {
          ...state.authInfo,
          [configId]: authInfo,
        },
      }));
    },

    getAuthInfo: (configId: string) => {
      return get().authInfo[configId];
    },

    clearAuthInfo: (configId: string) => {
      set((state) => {
        const newAuthInfo = { ...state.authInfo };
        delete newAuthInfo[configId];
        return { authInfo: newAuthInfo };
      });
    },

    isAuthValid: (configId: string) => {
      const authInfo = get().authInfo[configId];
      if (!authInfo) return false;

      const now = new Date();
      const expiresAt = new Date(authInfo.expiresAt);
      return now < expiresAt;
    },

    // 监控相关操作
    setMonitoringStatus: (configId: string, status: MonitoringStatus) => {
      set((state) => ({
        monitoringStatus: {
          ...state.monitoringStatus,
          [configId]: status,
        },
      }));
    },

    getMonitoringStatus: (configId: string) => {
      return get().monitoringStatus[configId];
    },

    clearMonitoringStatus: (configId: string) => {
      set((state) => {
        const newMonitoringStatus = { ...state.monitoringStatus };
        delete newMonitoringStatus[configId];
        return { monitoringStatus: newMonitoringStatus };
      });
    },

    // 手动持久化监控状态
    persistMonitoringStatus: async () => {
      const monitoringStatus = get().monitoringStatus;
      try {
        await SecureStore.setItemAsync(STORAGE_KEYS.MONITORING_STATUS, JSON.stringify(monitoringStatus));
        console.log('Monitoring status persisted successfully');
      } catch (error) {
        console.error('Failed to persist monitoring status:', error);
      }
    },

    // 中转服务器相关操作
    setProxyServer: (proxyServer: ProxyServerConfig | null) => {
      set({ proxyServer });
    },

    getProxyServer: () => {
      return get().proxyServer;
    },

    // 服务器配置相关操作
    setServerConfig: (configId: string, serverConfig: any) => {
      set((state) => ({
        serverConfig: {
          ...state.serverConfig,
          [configId]: serverConfig,
        },
      }));
    },

    getServerConfig: (configId: string) => {
      return get().serverConfig[configId];
    },

    clearServerConfig: (configId: string) => {
      set((state) => {
        const newServerConfig = { ...state.serverConfig };
        delete newServerConfig[configId];
        return { serverConfig: newServerConfig };
      });
    },

    // 数据加载
    loadData: async () => {
      try {
        set({ isLoading: true });

        // 并行加载所有数据
        const [configsData, groupsData, settingsData, selectedGroupData, monitoringStatusData] = await Promise.all([
          SecureStore.getItemAsync(STORAGE_KEYS.CONFIGS),
          SecureStore.getItemAsync(STORAGE_KEYS.GROUPS),
          SecureStore.getItemAsync(STORAGE_KEYS.SETTINGS),
          SecureStore.getItemAsync(STORAGE_KEYS.SELECTED_GROUP),
          SecureStore.getItemAsync(STORAGE_KEYS.MONITORING_STATUS),
        ]);

        const configs = configsData ? JSON.parse(configsData) : [];
        const groups = groupsData ? JSON.parse(groupsData) : DEFAULT_GROUPS;
        const settings = settingsData ? JSON.parse(settingsData) : DEFAULT_SETTINGS;
        const selectedGroupId = selectedGroupData || 'all';
        const monitoringStatus = monitoringStatusData ? JSON.parse(monitoringStatusData) : {};

        set({
          configs,
          groups,
          settings,
          selectedGroupId,
          monitoringStatus,
          isLoading: false,
        });
      } catch (error) {
        console.error('Failed to load data:', error);
        set({ isLoading: false });
      }
    },

    // 获取过滤后的配置
    getFilteredConfigs: () => {
      const { configs, selectedGroupId } = get();

      if (!selectedGroupId || selectedGroupId === 'all') {
        // 如果没有选中分组或选中的是"全部"分组，返回所有配置
        return configs;
      }

      // 筛选包含选中分组ID的配置
      return configs.filter((config) => config.groupIds.includes(selectedGroupId));
    },

    // 清空所有数据（用于重置应用状态）
    clearAllData: async () => {
      try {
        await Promise.all([
          SecureStore.deleteItemAsync(STORAGE_KEYS.CONFIGS),
          SecureStore.deleteItemAsync(STORAGE_KEYS.GROUPS),
          SecureStore.deleteItemAsync(STORAGE_KEYS.SETTINGS),
          SecureStore.deleteItemAsync(STORAGE_KEYS.SELECTED_GROUP),
          SecureStore.deleteItemAsync(STORAGE_KEYS.MONITORING_STATUS),
        ]);

        // 重置到初始状态
        set({
          configs: [],
          groups: DEFAULT_GROUPS,
          settings: DEFAULT_SETTINGS,
          selectedGroupId: 'all',
          isLoading: false,
          authInfo: {},
          monitoringStatus: {},
          proxyServer: null,
          serverConfig: {},
        });

        console.log('All data cleared successfully');
      } catch (error) {
        console.error('Failed to clear data:', error);
      }
    },
  }))
);

// 监听状态变化并持久化数据
useAppStore.subscribe(
  (state) => state.configs,
  (configs) => {
    SecureStore.setItemAsync(STORAGE_KEYS.CONFIGS, JSON.stringify(configs));
  }
);

useAppStore.subscribe(
  (state) => state.groups,
  (groups) => {
    SecureStore.setItemAsync(STORAGE_KEYS.GROUPS, JSON.stringify(groups));
  }
);

useAppStore.subscribe(
  (state) => state.settings,
  (settings) => {
    SecureStore.setItemAsync(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
  }
);

useAppStore.subscribe(
  (state) => state.selectedGroupId,
  (selectedGroupId) => {
    if (selectedGroupId) {
      SecureStore.setItemAsync(STORAGE_KEYS.SELECTED_GROUP, selectedGroupId);
    }
  }
);

