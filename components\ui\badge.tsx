import * as Slot from '@rn-primitives/slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { View, ViewProps } from 'react-native';
import { cn } from '~/lib/utils';
import { TextClassContext } from '~/components/ui/text';

const badgeVariants = cva(
  'web:inline-flex items-center rounded-full border border-border px-2.5 py-0.5 web:transition-colors web:focus:outline-none web:focus:ring-2 web:focus:ring-ring web:focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary web:hover:opacity-80 active:opacity-80',
        secondary: 'border-transparent bg-secondary web:hover:opacity-80 active:opacity-80',
        destructive: 'border-transparent bg-destructive web:hover:opacity-80 active:opacity-80',
        outline: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

const badgeTextVariants = cva('text-xs font-semibold ', {
  variants: {
    variant: {
      default: 'text-primary-foreground',
      secondary: 'text-secondary-foreground',
      destructive: 'text-destructive-foreground',
      outline: 'text-foreground',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

type BadgeProps = ViewProps & {
  asChild?: boolean;
  color?: string;
} & VariantProps<typeof badgeVariants>;

function Badge({ className, variant, asChild, color, ...props }: BadgeProps) {
  const Component = asChild ? Slot.View : View;
  
  // 如果传入了color，使用default样式并通过style设置背景色
  const finalVariant = color ? 'default' : variant;
  const badgeClasses = badgeVariants({ variant: finalVariant });
  const finalClassName = color 
    ? cn(badgeClasses.replace('bg-primary', ''), className) // 移除bg-primary类
    : cn(badgeClasses, className);
  
  const finalStyle = { backgroundColor: color }
  
  return (
    <TextClassContext.Provider value={badgeTextVariants({ variant: finalVariant })}>
      <Component className={finalClassName} style={finalStyle} {...props} />
    </TextClassContext.Provider>
  );
}

export { Badge, badgeTextVariants, badgeVariants };
export type { BadgeProps };