import * as React from 'react';
import { TextInput, StyleSheet, Platform, Dimensions, useColorScheme } from 'react-native';
import type { TextInputProps, TextStyle } from 'react-native';

interface InputProps extends TextInputProps {
  placeholderClassName?: string;
}

const { width } = Dimensions.get('window');
const isLargeScreen = width >= 1024;

const Input: React.FC<InputProps> = ({ style, placeholderTextColor, editable = true, ...props }) => {
  const colorScheme = useColorScheme();
  
  const isDark = colorScheme === 'dark';

  const getInputStyle = (): TextStyle => {
    const baseStyle = styles.input;
    const disabledStyle = editable === false ? styles.inputDisabled : {};
    const responsiveStyle = isLargeScreen ? styles.inputLarge : {};
    const themeStyle = isDark ? styles.inputDark : styles.inputLight;

    return StyleSheet.flatten([
      baseStyle,
      themeStyle,
      disabledStyle,
      responsiveStyle,
      style
    ]);
  };

  return (
    <TextInput
      style={getInputStyle()}
      editable={editable}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    height: 42,
    width: '100%',
    borderRadius: 6,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: Platform.OS === 'ios' ? 8 : 4,
    fontSize: 16,
    ...Platform.select({
      ios: {
        shadowColor: 'transparent',
      },
      android: {
        elevation: 0,
        includeFontPadding: false,
        textAlignVertical: 'center',
      },
      web: {
        outline: 'none',
        cursor: 'text',
      },
    }),
  },

  inputLight: {
    borderColor: '#e4e4e7',
    backgroundColor: '#ffffff',
    
  },

  inputDark: {
    borderColor: '#27272a',
    backgroundColor: 'black',
    color: 'white'
  },

  inputDisabled: {
    opacity: 0.5,
    ...Platform.select({
      web: {
        cursor: 'not-allowed',
      },
    }),
  },

  inputLarge: {
    fontSize: 16,
  },
});

export { Input };