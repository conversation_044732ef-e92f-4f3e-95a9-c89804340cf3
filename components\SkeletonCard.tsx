import React from "react";
import { View } from "react-native";
import { Skeleton } from "./ui/skeleton";
import { useThemeColor } from "~/hooks/useThemeColor";

export function SkeletonCard() {
    const backgroundColor = useThemeColor({}, 'background');
    const borderColor = useThemeColor({}, 'border');
    
    return (<View className="border rounded-lg p-3 py-2" style={{ backgroundColor, borderColor }}>
        {/* 标题和状态骨架 */}
        <View className="flex-row justify-between items-start mb-3">
          <View className="flex-1">
            <View className="flex-row items-center mb-2">
              <Skeleton className="w-32 h-5 mr-2" />
              <Skeleton className="w-20 h-5 rounded-xl" />
            </View>
          </View>
          <Skeleton className="w-16 h-6 rounded-xl" />
        </View>

        {/* 图表骨架 */}
        <View className="flex-row justify-around mb-3">
          <View className="items-center">
            <Skeleton className="w-24 h-24 rounded-full mb-2" />
            <Skeleton className="w-16 h-4" />
          </View>
          <View className="items-center">
            <Skeleton className="w-24 h-24 rounded-full mb-2" />
            <Skeleton className="w-20 h-4" />
          </View>
          <View className="items-center">
            <Skeleton className="w-24 h-24 rounded-full mb-2" />
            <Skeleton className="w-20 h-4" />
          </View>
        </View>

        {/* 网络统计骨架 */}
        <View className="mt-2">
          <View className="flex-row justify-between items-center">
            <Skeleton className="w-32 h-4" />
            <Skeleton className="w-32 h-4" />
          </View>
        </View>
      </View>)
}