import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { Config, MonitoringStatus, SUIConfig, ThreeXUIConfig, XUIConfig } from '@/lib/types';
import SimpleThreeXUIMonitorCard from '@/panels/3x-ui/components/SimpleMonitorCard';
import SimpleSUIMonitorCard from '@/panels/s-ui/components/SimpleMonitorCard';
import SimpleXUIMonitorCard from '@/panels/x-ui/components/SimpleMonitorCard';
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { router, useFocusEffect } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Alert, AppState, StyleSheet, View } from 'react-native';
import { Button } from './ui/button';

// 导入各面板的工具函数
import { getThreeXUIServerStatus } from '@/panels/3x-ui/utils';
import { getXUIServerStatus } from '@/panels/x-ui/utils';
import { getSUIServerStatus } from '@/panels/s-ui/utils';

interface MonitorCardContainerProps {
  configs: Config[];
}

export default function MonitorCardContainer({ configs }: MonitorCardContainerProps) {
  const { t } = useTranslation();
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const {
    deleteConfig,
    setMonitoringStatus,
    getMonitoringStatus,
    getProxyServer,
    persistMonitoringStatus
  } = useAppStore();

  // 轮询状态
  const intervalRef = useRef<number | null>(null);
  const lastUpdateTimeRef = useRef<Record<string, number>>({});
  const failureCountRef = useRef<Record<string, number>>({});

  // BottomSheet 状态
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["15%"], []);
  const [selectedConfig, setSelectedConfig] = useState<Config | null>(null);



  // 获取单个配置的服务器状态
  const fetchServerStatus = async (config: Config) => {
    const requestTime = Date.now();

    try {
      let serverStatus = null;
      switch (config.type) {
        case '3x-ui':
          serverStatus = await getThreeXUIServerStatus(config as ThreeXUIConfig);
          break;
        case 'x-ui':
          serverStatus = await getXUIServerStatus(config as XUIConfig);
          break;
        case 's-ui':
          serverStatus = await getSUIServerStatus(config as SUIConfig);
          break;
      }

      if (!serverStatus) {
        throw new Error('Failed to get server status');
      }

      // 检查是否是最新的请求响应
      if (requestTime < (lastUpdateTimeRef.current[config.id] || 0)) {
        console.log('Ignoring outdated response for config:', config.id);
        return;
      }

      // 更新最后更新时间
      lastUpdateTimeRef.current[config.id] = requestTime;

      // 重置失败计数
      failureCountRef.current[config.id] = 0;

      const newStatus: MonitoringStatus = {
        isOnline: true,
        lastUpdate: new Date(requestTime).toISOString(),
        failureCount: 0,
        serverStatus,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to fetch server status for config:', config.id, error);

      // 增加失败计数
      const currentFailures = (failureCountRef.current[config.id] || 0) + 1;
      failureCountRef.current[config.id] = currentFailures;

      // 3次失败后标记为离线
      if (currentFailures >= 3) {
        const offlineStatus: MonitoringStatus = {
          isOnline: false,
          lastUpdate: new Date().toISOString(),
          failureCount: currentFailures,
        };
        setMonitoringStatus(config.id, offlineStatus);
      }
    }
  };

  // 获取所有配置的服务器状态
  const fetchAllServerStatus = async () => {
    // TODO: 如果设置了中转服务器，使用中转服务器获取状态
    const proxyServer = getProxyServer();
    if (proxyServer) {
      // 中转服务器逻辑暂不实现
      console.log('Proxy server configured but not implemented yet');
      return;
    }

    // 直接轮询各个服务器
    const promises = configs.map(config => fetchServerStatus(config));
    await Promise.allSettled(promises);
  };

  // 开始轮询
  const startPolling = async () => {
    // 如果已经在轮询，直接返回
    if (intervalRef.current) return;

    // 立即获取一次状态
    await fetchAllServerStatus();

    // 设置轮询间隔 - 每1.5秒轮询一次
    intervalRef.current = setInterval(async () => {
      await fetchAllServerStatus();
    }, 1500);
  };

  // 停止轮询
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 使用 useFocusEffect 管理轮询
  useFocusEffect(
    useCallback(() => {
      // 从store中恢复已有的监控状态
      configs.forEach(config => {
        const existingStatus = getMonitoringStatus(config.id);
        if (existingStatus) {
          // 状态已经在store中，无需额外操作
        }
      });

      // 当组件获得焦点时开始轮询
      startPolling();

      return () => {
        // 当组件失去焦点时停止轮询并持久化监控状态
        stopPolling();
        persistMonitoringStatus();
      };
    }, [configs, persistMonitoringStatus])
  );

  // 监听应用状态变化
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState !== 'active') {
        // 应用进入后台或非活跃状态时，手动持久化监控状态
        await persistMonitoringStatus();
        // 停止轮询
        stopPolling();
      } else {
        // 应用回到前台时，如果没有在轮询，重新开始轮询
        if (!intervalRef.current) {
          startPolling();
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [persistMonitoringStatus]);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 处理卡片点击
  const handleCardPress = useCallback((config: Config) => {
    router.replace(`/${config.type}/${config.id}` as any);
  }, []);

  // 处理卡片长按
  const handleCardLongPress = useCallback((config: Config) => {
    setSelectedConfig(config);
    bottomSheetModalRef.current?.present();
  }, []);

  // 处理编辑配置
  const handleEdit = useCallback(() => {
    if (!selectedConfig) return;

    bottomSheetModalRef.current?.dismiss();
    router.push({
      pathname: '/config-form',
      params: {
        configType: selectedConfig.type,
        configId: selectedConfig.id,
      },
    });
  }, [selectedConfig]);

  // 处理删除配置
  const handleDelete = useCallback(() => {
    if (!selectedConfig) return;

    bottomSheetModalRef.current?.dismiss();
    Alert.alert(
      t('common.confirm'),
      t('common.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteConfig(selectedConfig.id);
            } catch (error) {
              console.error('Failed to delete config:', error);
              Alert.alert(t('common.error'), t('common.deleteError'));
            }
          },
        },
      ]
    );
  }, [selectedConfig, t, deleteConfig]);

  const renderMonitorCard = (config: Config) => {
    switch (config.type) {
      case 's-ui':
        return <SimpleSUIMonitorCard config={config as SUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      case 'x-ui':
        return <SimpleXUIMonitorCard config={config as XUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      case '3x-ui':
        return <SimpleThreeXUIMonitorCard config={config as ThreeXUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      default:
        return null;
    }
  };

  return (
    <>
      {configs.map((config) => (
        <React.Fragment key={config.id}>
          {renderMonitorCard(config)}
        </React.Fragment>
      ))}
      
      {/* BottomSheet */}
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[styles.bottomSheetContent, { backgroundColor }]}>

          <View style={styles.buttonsContainer}>
            <Button
              variant='ghost'
              onPress={handleEdit}
            >
              <Text style={[styles.buttonText, { color: textColor }]}>
                {t('common.edit')}
              </Text>
            </Button>

            <View style={[styles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant='ghost'
              onPress={handleDelete}
            >
              <Text style={[styles.buttonText, styles.deleteButtonText]}>
                {t('common.delete')}
              </Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    </>
  );
}

const styles = StyleSheet.create({
  bottomSheetContent: {
    padding: 0,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});
