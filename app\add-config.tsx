import { router } from 'expo-router';
import React from 'react';

import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { ConfigType, configTypes } from '@/lib/types';
import { SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const handleTypeSelect = (type: ConfigType) => {
    router.push({
      pathname: '/config-form',
      params: { configType: type },
    })
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <View style={styles.typeList}>
          {configTypes.map((configType) => (
            <View key={configType}>
              
              <TouchableOpacity
                style={styles.typeItem}
                onPress={() => handleTypeSelect(configType)}
              >
                <Text className="text-l font-semibold">
                  {t(`configTypes.${configType}`)}
                </Text>
              </TouchableOpacity>
              <View style={[styles.separator, { backgroundColor: borderColor }]} />
            </View>
          ))}
        </View>
      </ScrollView>


    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
    padding: 0,
  },
  header: {
    marginBottom: 24,
  },
  typeList: {
    flex: 1,
  },
  typeItem: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    // backgroundColor will inherit from parent
  },
  separator: {
    height: 0.5,
    // backgroundColor will be set dynamically
  },
});
