import { Drawer } from 'expo-router/drawer';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Home, List, Route, ArrowLeftRight, Menu } from 'lucide-react-native';
import React from 'react';
import { DrawerContentScrollView, DrawerItemList, DrawerItem } from '@react-navigation/drawer';
import { View, StyleSheet } from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';

// 自定义 Drawer Content 组件
function CustomDrawerContent(props: any) {
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const handleBackToHome = () => {
    // 使用 router.navigate 返回主页
    router.replace("/")
  };

  return (
    <View style={[styles.drawerContainer, { backgroundColor }]}>
      <DrawerContentScrollView {...props} style={styles.scrollView}>
        <DrawerItemList {...props} />
      </DrawerContentScrollView>

      {/* Footer 区域 */}
      <View style={[styles.footer, { borderTopColor: borderColor }]}>
        <DrawerItem
          label="返回主页"
          onPress={handleBackToHome}
          icon={({ color, size }) => <Home color={color} size={size} />}
          labelStyle={{ color: textColor, fontSize: 16, fontWeight: '500' }}
          activeTintColor={textColor}
          inactiveTintColor={textColor + '80'}
        />
      </View>
    </View>
  );
}

export default function ThreeXUILayout() {
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const { configId } = useLocalSearchParams<{ configId: string }>();

  return (
    <>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          drawerStyle: {
            backgroundColor,
            borderRightColor: borderColor,
            borderRightWidth: 1,
          },
          drawerLabelStyle: {
            color: textColor,
            fontSize: 16,
            fontWeight: '500',
          },
          drawerActiveTintColor: textColor,
          drawerInactiveTintColor: textColor + '80',
          headerTintColor: textColor,
          headerTitleStyle: {
            color: textColor,
            fontSize: 18,
            fontWeight: '600',
          },
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: '概述',
            title: '3X-UI 概述',
            drawerIcon: ({ color, size }) => (
              <Home color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="inbounds"
          options={{
            drawerLabel: '入站列表',
            title: '入站列表',
            drawerIcon: ({ color, size }) => (
              <List color={color} size={size} />
            ),            
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="routing"
          options={{
            drawerLabel: '路由规则',
            title: '路由规则',
            drawerIcon: ({ color, size }) => (
              <Route color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="outbounds"
          options={{
            drawerLabel: '出站路由',
            title: '出站路由',
            drawerIcon: ({ color, size }) => (
              <ArrowLeftRight color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
      </Drawer>
    </>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  footer: {
    borderTopWidth: 1,
    paddingTop: 8,
    paddingBottom: 24,
  },
});
