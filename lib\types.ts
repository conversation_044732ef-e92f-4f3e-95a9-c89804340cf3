// 配置类型定义
export const configTypes = ['s-ui', 'x-ui', '3x-ui'] as const;
export type ConfigType = (typeof configTypes)[number];

export type ProtocolType = 'http' | 'https';

export interface BaseConfig {
  id: string;
  name: string;
  type: ConfigType;
  url: string;
  groupIds: string[]; // 改为数组，支持多分组关联
  createdAt: string;
  updatedAt: string;
}

export interface SUIConfig extends BaseConfig {
  type: 's-ui';
  api: string;
  protocol: ProtocolType;
  cert?: string;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export interface XUIConfig extends BaseConfig {
  type: 'x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  cert?: string;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export interface ThreeXUIConfig extends BaseConfig {
  type: '3x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  cert?: string;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export type Config = SUIConfig | XUIConfig | ThreeXUIConfig;

// 认证信息类型定义
export interface AuthInfo {
  expiresAt: string; // ISO 字符串，默认30分钟后过期
  isPinned?: boolean; // 是否已固定公钥证书
}

// 服务器状态监控数据类型定义
export interface ServerStatus {
  cpu: number;
  cpuCores: number;
  logicalPro: number;
  cpuSpeedMhz: number;
  mem: {
    current: number;
    total: number;
  };
  swap: {
    current: number;
    total: number;
  };
  disk: {
    current: number;
    total: number;
  };
  xray: {
    state: string;
    errorMsg: string;
    version: string;
  };
  uptime: number;
  loads: number[];
  tcpCount: number;
  udpCount: number;
  netIO: {
    up: number;
    down: number;
  };
  netTraffic: {
    sent: number;
    recv: number;
  };
  publicIP: {
    ipv4: string;
    ipv6: string;
  };
  appStats: {
    threads: number;
    mem: number;
    uptime: number;
  };
}

// 监控状态类型定义
export interface MonitoringStatus {
  isOnline: boolean;
  lastUpdate: string;
  failureCount: number;
  serverStatus?: ServerStatus;
}

// 分组类型定义
export interface Group {
  id: string;
  name: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'system';

// 语言类型定义
export type Language = 'en' | 'zh-CN' | 'zh-TW' | 'fa';

// 应用设置类型定义
export interface AppSettings {
  theme: ThemeMode;
  language: Language;
  isPro: boolean;
}

// 中转服务器配置类型定义
export interface ProxyServerConfig {
  url: string;
  token: string;
}



// 应用状态类型定义
export interface AppState {
  configs: Config[];
  groups: Group[];
  settings: AppSettings;
  selectedGroupId: string | null;
  isLoading: boolean;
  // 认证信息存储，以配置ID为键
  authInfo: Record<string, AuthInfo>;
  // 监控状态存储，以配置ID为键
  monitoringStatus: Record<string, MonitoringStatus>;
  // 中转服务器配置
  proxyServer: ProxyServerConfig | null;
  // 服务器配置存储，以配置ID为键
  serverConfig: Record<string, any>;
}

// 表单数据类型定义
export interface ConfigFormData {
  name: string;
  url: string;
  protocol: ProtocolType;
  groupId: string | string[]; // 支持单选或多选分组
  // S-UI 特有字段
  api?: string;
  // X-UI 和 3X-UI 特有字段
  username?: string;
  password?: string;
  cert?: string;
  // 处理后的证书指纹数组
  certFingerprints?: string[];
}



//协议相关
interface InboundConfig {
  id: number;
  port: number;
  protocol: string;
  settings: string;
  streamSettings: string;
  tag: string;
  clientStats: ClientStat[];
  enable: boolean;
}

interface ClientStat {
  id: number;
  email: string;
  enable: boolean;
  expiryTime: number;
  total: number;
  reset: number;
}

interface VlessClient {
  id: string;
  email: string;
  enable: boolean;
  flow?: string;
  subId: string;
  comment?: string;
}

interface VmessClient {
  id: string;
  email: string;
  enable: boolean;
  alterId?: number;
  subId: string;
  comment?: string;
}

interface TrojanClient {
  password: string;
  email: string;
  enable: boolean;
  subId: string;
}

interface ShadowsocksClient {
  password: string;
  email: string;
  enable: boolean;
  subId: string;
}



interface ProxyAccount {
  user: string;
  pass: string;
}

export type {
  InboundConfig,
  ClientStat,
  VlessClient,
  VmessClient,
  TrojanClient,
  ShadowsocksClient,
  ProxyAccount
};