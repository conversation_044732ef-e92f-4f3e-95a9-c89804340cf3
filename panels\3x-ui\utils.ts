import { AuthInfo, ConfigFormData, ProxyAccount, ServerStatus, ShadowsocksClient, ThreeXUIConfig, TrojanClient, VlessClient, VmessClient } from '@/lib/types';
import { SSLPinningManager, smartFetch } from '@/lib/utils';
import { useAppStore } from '@/lib/store';
import { InboundConfig, WireguardPeer } from './types';

/**
 * 3X-UI面板API验证接口响应类型
 */
interface ThreeXUILoginResponse {
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * 3X-UI面板API通用响应类型
 */
interface ThreeXUIApiResponse<T = any> {
  success: boolean;
  msg: string;
  obj?: T;
}

/**
 * 验证3X-UI面板API连接
 * @param formData 配置表单数据
 * @param certFingerprints 可选的证书指纹数组
 * @returns Promise<boolean> 返回验证是否成功
 */
export async function validateThreeXUIConnection(
  formData: ConfigFormData,
  publicKeyHashes?: string[]
): Promise<boolean> {
  try {
    const { protocol, url, username, password } = formData;
    
    if (!url || !username || !password) {
      throw new Error('URL, username and password are required');
    }

    // 构建完整的API URL
    const baseUrl = `${protocol}://${url}`;
    const loginUrl = `${baseUrl}/login`;

    // 准备请求头
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');

    // 准备请求体 - 3X-UI使用用户名密码进行验证
    const urlencoded = new URLSearchParams();
    urlencoded.append('username', username);
    urlencoded.append('password', password);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: urlencoded.toString(), // 转换为字符串
      redirect: 'follow'
    };

    // 如果有公钥哈希，初始化SSL固定
    if (publicKeyHashes && publicKeyHashes.length > 0) {
      const urlObj = new URL(loginUrl);
      await SSLPinningManager.initializePinning(urlObj.hostname, publicKeyHashes);
    }

    // 发送请求 - SSL固定会自动应用
    const response = await fetch(loginUrl, requestOptions);
    
    // 检查HTTP状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 解析响应
    let result: ThreeXUILoginResponse;
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      result = await response.json();
    } else {
      // 如果不是JSON响应，尝试解析文本
      const text = await response.text();
      try {
        result = JSON.parse(text);
      } catch {
        // 如果无法解析为JSON，验证失败
        throw new Error('Invalid response format: expected JSON');
      }
    }

    // 检查业务逻辑成功标志
    return result.success === true;

  } catch (error) {
    console.error('3X-UI connection validation failed:', error);

    // 提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Fetch error details:', {
        message: error.message,
        stack: error.stack,
        formData: { url: formData.url, protocol: formData.protocol },
        publicKeyHashes: publicKeyHashes?.length || 0
      });
    }

    return false;
  }
}

/**
 * 构建3X-UI面板的完整配置URL
 * @param protocol 协议类型
 * @param url 基础URL
 * @returns 完整的面板URL
 */
export function buildThreeXUIUrl(protocol: string, url: string): string {
  return `${protocol}://${url}`;
}

/**
 * 验证3X-UI配置表单数据
 * @param formData 表单数据
 * @returns 验证结果和错误信息
 */
export function validateThreeXUIFormData(formData: ConfigFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!formData.name?.trim()) {
    errors.push('Configuration name is required');
  }

  if (!formData.url?.trim()) {
    errors.push('URL is required');
  } else {
    try {
      // 验证URL格式
      new URL(`${formData.protocol}://${formData.url}`);
    } catch {
      errors.push('Invalid URL format');
    }
  }

  if (!formData.username?.trim()) {
    errors.push('Username is required');
  }

  if (!formData.password?.trim()) {
    errors.push('Password is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 3X-UI面板登录并获取认证信息
 * @param config 3X-UI配置
 * @returns Promise<AuthInfo | null> 返回认证信息或null
 */
export async function loginThreeXUI(config: ThreeXUIConfig): Promise<AuthInfo | null> {
  try {
    const baseUrl = `${config.protocol}://${config.url}`;
    const loginUrl = `${baseUrl}/login`;

    // 准备请求头
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');

    // 准备请求体
    const urlencoded = new URLSearchParams();
    urlencoded.append('username', config.username);
    urlencoded.append('password', config.password);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: urlencoded.toString(),
      credentials: 'include'
    };



    // 发送请求
    const response = await fetch(loginUrl, requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: ThreeXUILoginResponse = await response.json();

    if (result.success) {
      // 创建认证信息，默认30分钟过期
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 30);

      const authInfo = {
        expiresAt: expiresAt.toISOString(),
        isPinned: false
      };

      console.log('3X-UI Auth Info Created:', authInfo);
      return authInfo;
    }

    return null;
  } catch (error) {
    console.error('3X-UI login failed:', error);
    return null;
  }
}

/**
 * 获取3X-UI服务器状态
 * @param config 3X-UI配置
 * @returns Promise<ServerStatus | null> 返回服务器状态或null
 */
export async function getThreeXUIServerStatus(config: ThreeXUIConfig): Promise<ServerStatus | null> {
  try {
    const store = useAppStore.getState();

    const baseUrl = `${config.protocol}://${config.url}`;
    const statusUrl = `${baseUrl}/server/status`;

    // 获取或创建认证信息
    let authInfo = store.getAuthInfo(config.id);

    // 如果有证书指纹且尚未固定，先进行SSL固定（在登录之前确保安全）
    if (config.certFingerprints && config.certFingerprints.length > 0 && (!authInfo || !authInfo.isPinned)) {
      const urlObj = new URL(statusUrl);
      const pinned = await SSLPinningManager.initializePinning(urlObj.hostname, config.certFingerprints);
      if (pinned && authInfo) {
        // 只更新现有authInfo中的固定状态，不创建新的authInfo
        authInfo.isPinned = true;
        store.setAuthInfo(config.id, authInfo);
      }
    }

    // 确保认证信息有效
    if (!authInfo || !store.isAuthValid(config.id)) {
      // 需要重新登录
      const newAuthInfo = await loginThreeXUI(config);
      if (!newAuthInfo) {
        throw new Error('Authentication failed');
      }
      // 保持SSL固定状态
      newAuthInfo.isPinned = authInfo?.isPinned || false;
      authInfo = newAuthInfo;
      store.setAuthInfo(config.id, authInfo);
    }

    // 准备请求头
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      credentials: 'include'
    };

    // 发送请求
    let response = await fetch(statusUrl, requestOptions);

    // 如果返回401，尝试重新登录
    if (response.status === 401) {
      console.log('Received 401, attempting re-authentication for 3X-UI...');
      const newAuthInfo = await loginThreeXUI(config);
      if (newAuthInfo) {
        // 保持SSL固定状态
        newAuthInfo.isPinned = authInfo?.isPinned || false;
        authInfo = newAuthInfo;
        store.setAuthInfo(config.id, authInfo);

        // 重试请求
        response = await fetch(statusUrl, requestOptions);
      }
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success && result.obj) {
      return result.obj as ServerStatus;
    }

    return null;
  } catch (error) {
    console.error('3X-UI get server status failed:', error);
    return null;
  }
}

/**
 * 解析入站配置中被字符串化的字段
 * @param inbound 原始入站配置
 * @returns 解析后的入站配置
 */
export function parseInboundStringifiedFields(inbound: InboundConfig): InboundConfig {
  const parsed = { ...inbound };

  // 解析 settings 字段
  if (typeof parsed.settings === 'string') {
    try {
      parsed.settings = JSON.parse(parsed.settings);
    } catch (error) {
      console.warn('Failed to parse inbound settings:', error);
      parsed.settings = {};
    }
  }

  // 解析 streamSettings 字段
  if (typeof parsed.streamSettings === 'string') {
    try {
      parsed.streamSettings = JSON.parse(parsed.streamSettings);
    } catch (error) {
      console.warn('Failed to parse inbound streamSettings:', error);
      parsed.streamSettings = {};
    }
  }

  // 解析 sniffing 字段
  if (typeof parsed.sniffing === 'string') {
    try {
      parsed.sniffing = JSON.parse(parsed.sniffing);
    } catch (error) {
      console.warn('Failed to parse inbound sniffing:', error);
      parsed.sniffing = {
        enabled: false,
        destOverride: []
      };
    }
  }

  // 解析 allocate 字段
  if (typeof parsed.allocate === 'string') {
    try {
      parsed.allocate = JSON.parse(parsed.allocate);
    } catch (error) {
      console.warn('Failed to parse inbound allocate:', error);
      parsed.allocate = {
        strategy: 'always',
        refresh: 5,
        concurrency: 3
      };
    }
  }

  return parsed;
}

/**
 * 获取3X-UI入站配置列表并存储到serverConfig
 * @param config 3X-UI配置
 * @returns Promise<InboundConfig[] | null> 返回入站配置列表或null
 */
export async function getThreeXUIInboundList(config: ThreeXUIConfig): Promise<InboundConfig[] | null> {
  try {
    const baseUrl = `${config.protocol}://${config.url}`;
    const listUrl = `${baseUrl}/panel/inbound/list`;

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      credentials: 'include'
    };

    const response = await smartFetch(listUrl, requestOptions, config);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: ThreeXUIApiResponse<InboundConfig[]> = await response.json();

    if (result.success && result.obj) {
      // 解析每个入站配置中被字符串化的字段
      const parsedInbounds = result.obj.map(parseInboundStringifiedFields);

      // 直接存储到serverConfig
      const store = useAppStore.getState();
      let serverConfig = store.getServerConfig(config.id);
      if (!serverConfig) {
        serverConfig = { inbounds: [] };
      }

      // 更新入站配置列表
      serverConfig.inbounds = parsedInbounds;
      store.setServerConfig(config.id, serverConfig);

      return parsedInbounds;
    }

    return null;
  } catch (error) {
    console.error('3X-UI get inbound list failed:', error);
    return null;
  }
}

/**
 * 获取3X-UI Xray配置并存储到serverConfig
 * @param config 3X-UI配置
 * @returns Promise<any | null> 返回Xray配置或null
 */
export async function getThreeXUIXrayConfig(config: ThreeXUIConfig): Promise<any | null> {
  try {
    const baseUrl = `${config.protocol}://${config.url}`;
    const xrayUrl = `${baseUrl}/panel/xray/`;

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      credentials: 'include'
    };

    const response = await smartFetch(xrayUrl, requestOptions, config);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: ThreeXUIApiResponse<string> = await response.json();

    if (result.success && result.obj) {
      // 解析字符串化的xray配置
      const xrayConfig = JSON.parse(result.obj);

      // 直接存储到serverConfig
      const store = useAppStore.getState();
      let serverConfig = store.getServerConfig(config.id);
      if (!serverConfig) {
        serverConfig = {};
      }

      // 更新xray配置
      serverConfig.xray = xrayConfig.xraySetting;
      store.setServerConfig(config.id, serverConfig);

      return xrayConfig;
    }

    return null;
  } catch (error) {
    console.error('3X-UI get xray config failed:', error);
    return null;
  }
}

export class XUIConfigConverter {
  private serverHost: string;

  constructor(serverHost: string) {
    // 清理 serverHost，移除可能的协议前缀
    this.serverHost = serverHost.replace(/^https?:\/\//, '');
  }

  /**
   * 将入站配置转换为客户端协议链接
   */
  convertToClientLinks(inboundConfig: any): string[] {
    const links: string[] = [];
    
    switch (inboundConfig.protocol.toLowerCase()) {
      case 'vless':
        links.push(...this.convertVless(inboundConfig));
        break;
      case 'vmess':
        links.push(...this.convertVmess(inboundConfig));
        break;
      case 'trojan':
        links.push(...this.convertTrojan(inboundConfig));
        break;
      case 'shadowsocks':
        links.push(...this.convertShadowsocks(inboundConfig));
        break;
      case 'wireguard':
        links.push(...this.convertWireguard(inboundConfig));
        break;
      case 'socks':
        links.push(...this.convertSocks(inboundConfig));
        break;
      case 'http':
        links.push(...this.convertHttp(inboundConfig));
        break;
    }
    
    return links.filter(link => link !== '');
  }

  private convertVmess(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: VmessClient) => {
      const vmessConfig = {
        v: "2",
        ps: client.email || config.tag,
        add: this.serverHost,
        port: config.port.toString(),
        id: client.id,
        aid: (client.alterId || 0).toString(),
        scy: "auto",
        net: streamSettings.network || "tcp",
        type: "none",
        host: "",
        path: "",
        tls: streamSettings.security === "tls" ? "tls" : "",
        sni: "",
        alpn: "",
        fp: ""
      };
      
      // 根据不同的网络类型添加参数
      this.addVmessNetworkParams(vmessConfig, streamSettings);
      
      // 添加安全相关参数
      this.addVmessSecurityParams(vmessConfig, streamSettings);
      
      const vmessJson = JSON.stringify(vmessConfig);
      const encodedVmess = btoa(vmessJson);
      const link = `vmess://${encodedVmess}`;
      links.push(link);
    });
    
    return links;
  }

  private convertVless(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: VlessClient) => {   
      const params = new URLSearchParams();
      params.set('type', streamSettings.network || 'tcp');
      params.set('security', streamSettings.security || 'none');
      params.set('encryption', 'none');
      params.set('flow', client.flow || '');
      
      // 根据不同的网络类型添加参数
      this.addNetworkParams(params, streamSettings);
      
      const link = `vless://${client.id}@${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(client.email || config.tag)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertTrojan(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: TrojanClient) => { 
      const params = new URLSearchParams();
      params.set('type', streamSettings.network || 'tcp');
      params.set('security', streamSettings.security || 'none');
      
      this.addNetworkParams(params, streamSettings);
      
      const link = `trojan://${encodeURIComponent(client.password)}@${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(client.email || config.tag)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertShadowsocks(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.clients?.forEach((client: ShadowsocksClient) => { 
      const method = settings.method;
      const serverPassword = settings.password;
      const userPassword = client.password;
      
      // Shadowsocks 2022格式
      if (method.includes('2022')) {
        const userInfo = `${method}:${serverPassword}:${userPassword}`;
        const encodedUserInfo = btoa(userInfo);
        const link = `ss://${encodedUserInfo}@${this.serverHost}:${config.port}#${encodeURIComponent(client.email || config.tag)}`;
        links.push(link);
      } else {
        // 传统格式
        const userInfo = `${method}:${userPassword}`;
        const encodedUserInfo = btoa(userInfo);
        const link = `ss://${encodedUserInfo}@${this.serverHost}:${config.port}#${encodeURIComponent(client.email || config.tag)}`;
        links.push(link);
      }
    });
    
    return links;
  }

  private convertWireguard(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.peers?.forEach((peer: WireguardPeer, index: number) => {
      const params = new URLSearchParams();
      if (peer.privateKey) {
        params.set('privatekey', peer.privateKey);
      }
      params.set('publickey', peer.publicKey);
      params.set('address', peer.allowedIPs.join(','));
      params.set('mtu', settings.mtu?.toString() || '1420');

      if (peer.keepAlive && peer.keepAlive > 0) {
        params.set('keepalive', peer.keepAlive.toString());
      }
      
      const link = `wireguard://${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(`WG-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertSocks(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.accounts?.forEach((account: ProxyAccount, index: number) => {
      const link = `socks5://${encodeURIComponent(account.user)}:${encodeURIComponent(account.pass)}@${this.serverHost}:${config.port}#${encodeURIComponent(`SOCKS-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertHttp(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.accounts?.forEach((account: ProxyAccount, index: number) => {
      const link = `http://${encodeURIComponent(account.user)}:${encodeURIComponent(account.pass)}@${this.serverHost}:${config.port}#${encodeURIComponent(`HTTP-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private addVmessNetworkParams(vmessConfig: any, streamSettings: any): void {
    const network = streamSettings.network;
    
    switch (network) {
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          vmessConfig.type = 'http';
          if (streamSettings.tcpSettings.header.request?.headers?.Host) {
            vmessConfig.host = streamSettings.tcpSettings.header.request.headers.Host.join(',');
          }
          if (streamSettings.tcpSettings.header.request?.path) {
            vmessConfig.path = streamSettings.tcpSettings.header.request.path.join(',');
          }
        }
        break;
        
      case 'kcp':
        if (streamSettings.kcpSettings) {
          vmessConfig.type = streamSettings.kcpSettings.header?.type || 'none';
          if (streamSettings.kcpSettings.seed) {
            vmessConfig.path = streamSettings.kcpSettings.seed;
          }
        }
        break;
        
      case 'ws':
        if (streamSettings.wsSettings) {
          vmessConfig.path = streamSettings.wsSettings.path || '/';
          if (streamSettings.wsSettings.host) {
            vmessConfig.host = streamSettings.wsSettings.host;
          }
        }
        break;
        
      case 'httpupgrade':
        if (streamSettings.httpupgradeSettings) {
          vmessConfig.path = streamSettings.httpupgradeSettings.path || '/';
          if (streamSettings.httpupgradeSettings.host) {
            vmessConfig.host = streamSettings.httpupgradeSettings.host;
          }
        }
        break;
        
      case 'xhttp':
        if (streamSettings.xhttpSettings) {
          vmessConfig.path = streamSettings.xhttpSettings.path || '/';
          if (streamSettings.xhttpSettings.host) {
            vmessConfig.host = streamSettings.xhttpSettings.host;
          }
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          vmessConfig.path = streamSettings.grpcSettings.serviceName || '';
          if (streamSettings.grpcSettings.authority) {
            vmessConfig.host = streamSettings.grpcSettings.authority;
          }
          vmessConfig.type = streamSettings.grpcSettings.multiMode ? 'multi' : 'gun';
        }
        break;
        
      case 'h2':
        if (streamSettings.httpSettings) {
          vmessConfig.path = streamSettings.httpSettings.path || '/';
          if (streamSettings.httpSettings.host?.length > 0) {
            vmessConfig.host = streamSettings.httpSettings.host.join(',');
          }
        }
        break;
        
      case 'quic':
        if (streamSettings.quicSettings) {
          vmessConfig.type = streamSettings.quicSettings.header?.type || 'none';
          if (streamSettings.quicSettings.security) {
            vmessConfig.host = streamSettings.quicSettings.security;
          }
          if (streamSettings.quicSettings.key) {
            vmessConfig.path = streamSettings.quicSettings.key;
          }
        }
        break;
    }
  }

  private addVmessSecurityParams(vmessConfig: any, streamSettings: any): void {
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      vmessConfig.tls = 'tls';
      if (streamSettings.tlsSettings.serverName) {
        vmessConfig.sni = streamSettings.tlsSettings.serverName;
      }
      if (streamSettings.tlsSettings.alpn) {
        vmessConfig.alpn = streamSettings.tlsSettings.alpn.join(',');
      }
      if (streamSettings.tlsSettings.settings?.fingerprint) {
        vmessConfig.fp = streamSettings.tlsSettings.settings.fingerprint;
      }
    }
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      vmessConfig.tls = 'reality';
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        vmessConfig.sni = streamSettings.realitySettings.serverNames[0];
      }
      if (streamSettings.realitySettings.settings?.fingerprint) {
        vmessConfig.fp = streamSettings.realitySettings.settings.fingerprint;
      }
      if (streamSettings.realitySettings.settings?.publicKey) {
        vmessConfig.pbk = streamSettings.realitySettings.settings.publicKey;
      }
      if (streamSettings.realitySettings.shortIds?.length > 0) {
        vmessConfig.sid = streamSettings.realitySettings.shortIds[0];
      }
    }
  }

  private addNetworkParams(params: URLSearchParams, streamSettings: any): void {
    const network = streamSettings.network;
    
    switch (network) {
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          params.set('headerType', 'http');
        }
        break;
        
      case 'kcp':
        if (streamSettings.kcpSettings) {
          params.set('headerType', streamSettings.kcpSettings.header?.type || 'none');
          if (streamSettings.kcpSettings.seed) {
            params.set('seed', streamSettings.kcpSettings.seed);
          }
        }
        break;
        
      case 'ws':
        if (streamSettings.wsSettings) {
          params.set('path', streamSettings.wsSettings.path || '/');
          if (streamSettings.wsSettings.host) {
            params.set('host', streamSettings.wsSettings.host);
          }
        }
        break;
        
      case 'httpupgrade':
        if (streamSettings.httpupgradeSettings) {
          params.set('path', streamSettings.httpupgradeSettings.path || '/');
          if (streamSettings.httpupgradeSettings.host) {
            params.set('host', streamSettings.httpupgradeSettings.host);
          }
        }
        break;
        
      case 'xhttp':
        if (streamSettings.xhttpSettings) {
          params.set('path', streamSettings.xhttpSettings.path || '/');
          if (streamSettings.xhttpSettings.host) {
            params.set('host', streamSettings.xhttpSettings.host);
          }
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          params.set('serviceName', streamSettings.grpcSettings.serviceName || '');
          if (streamSettings.grpcSettings.authority) {
            params.set('authority', streamSettings.grpcSettings.authority);
          }
          params.set('mode', streamSettings.grpcSettings.multiMode ? 'multi' : 'gun');
        }
        break;
    }
    
    // 添加安全相关参数
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      if (streamSettings.tlsSettings.serverName) {
        params.set('sni', streamSettings.tlsSettings.serverName);
      }
      if (streamSettings.tlsSettings.alpn) {
        params.set('alpn', streamSettings.tlsSettings.alpn.join(','));
      }
      if (streamSettings.tlsSettings.settings?.fingerprint) {
        params.set('fp', streamSettings.tlsSettings.settings.fingerprint);
      }
    }
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      params.set('pbk', streamSettings.realitySettings.settings?.publicKey || '');
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        params.set('sni', streamSettings.realitySettings.serverNames[0]);
      }
      if (streamSettings.realitySettings.shortIds?.length > 0) {
        params.set('sid', streamSettings.realitySettings.shortIds[0]);
      }
      if (streamSettings.realitySettings.settings?.fingerprint) {
        params.set('fp', streamSettings.realitySettings.settings.fingerprint);
      }
      if (streamSettings.realitySettings.settings?.spiderX) {
        params.set('spx', streamSettings.realitySettings.settings.spiderX);
      }
    }
  }

  /**
   * 批量转换所有入站配置
   */
  convertAllConfigs(configs: any[]): { [key: string]: string[] } {
    const result: { [key: string]: string[] } = {};
    
    configs.forEach(config => {
      if (config.enable) {
        const links = this.convertToClientLinks(config);
        if (links.length > 0) {
          result[config.tag] = links;
        }
      }
    });
    
    return result;
  }

  /**
   * 生成订阅链接内容
   */
  generateSubscriptionContent(configs: any[]): string {
    const allLinks: string[] = [];
    
    configs.forEach(config => {
      if (config.enable) {
        const links = this.convertToClientLinks(config);
        allLinks.push(...links);
      }
    });
    
    return btoa(allLinks.join('\n'));
  }

  /**
   * 生成Clash配置
   */
  generateClashConfig(configs: any[]): any {
    const proxies: any[] = [];
    const proxyNames: string[] = [];
    
    configs.forEach(config => {
      if (!config.enable) return;
      
      const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
      const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
      
      switch (config.protocol.toLowerCase()) {
        case 'vless':
          settings.clients?.forEach((client: VlessClient) => {
            if (client.enable) {
              const proxy = this.generateClashVlessProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'vmess':
          settings.clients?.forEach((client: VmessClient) => {
            if (client.enable) {
              const proxy = this.generateClashVmessProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'trojan':
          settings.clients?.forEach((client: TrojanClient) => {
            if (client.enable) {
              const proxy = this.generateClashTrojanProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'shadowsocks':
          settings.clients?.forEach((client: ShadowsocksClient) => {
            if (client.enable) {
              const proxy = this.generateClashShadowsocksProxy(client, config, settings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
      }
    });
    
    return {
      proxies,
      'proxy-groups': [
        {
          name: 'Proxy',
          type: 'select',
          proxies: ['DIRECT', ...proxyNames]
        }
      ]
    };
  }

  private generateClashVmessProxy(client: VmessClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'vmess',
      server: this.serverHost,
      port: config.port,
      uuid: client.id,
      alterId: client.alterId || 0,
      cipher: 'auto',
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls',
      'skip-cert-verify': true
    };
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashVlessProxy(client: VlessClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'vless',
      server: this.serverHost,
      port: config.port,
      uuid: client.id,
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls' || streamSettings.security === 'reality',
      'skip-cert-verify': true
    };
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      proxy.reality = {
        'public-key': streamSettings.realitySettings.settings?.publicKey || '',
        'short-id': streamSettings.realitySettings.shortIds?.[0] || ''
      };
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        proxy.servername = streamSettings.realitySettings.serverNames[0];
      }
    }
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashTrojanProxy(client: TrojanClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'trojan',
      server: this.serverHost,
      port: config.port,
      password: client.password,
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls',
      'skip-cert-verify': true
    };
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashShadowsocksProxy(client: ShadowsocksClient, config: any, settings: any): any {
    return {
      name: client.email || config.tag,
      type: 'ss',
      server: this.serverHost,
      port: config.port,
      cipher: settings.method,
      password: client.password
    };
  }

  private addClashNetworkSettings(proxy: any, streamSettings: any): void {
    switch (streamSettings.network) {
      case 'ws':
        if (streamSettings.wsSettings) {
          proxy['ws-opts'] = {
            path: streamSettings.wsSettings.path || '/',
            headers: streamSettings.wsSettings.headers || {}
          };
          if (streamSettings.wsSettings.host) {
            proxy['ws-opts'].headers.Host = streamSettings.wsSettings.host;
          }
        }
        break;
        
      case 'h2':
        if (streamSettings.httpSettings) {
          proxy['h2-opts'] = {
            path: streamSettings.httpSettings.path || '/',
            host: streamSettings.httpSettings.host || []
          };
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          proxy['grpc-opts'] = {
            'grpc-service-name': streamSettings.grpcSettings.serviceName || ''
          };
        }
        break;
        
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          proxy['http-opts'] = {
            method: 'GET',
            path: streamSettings.tcpSettings.header.request?.path || ['/'],
            headers: streamSettings.tcpSettings.header.request?.headers || {}
          };
        }
        break;
    }
    
    // 添加TLS相关设置
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      if (streamSettings.tlsSettings.serverName) {
        proxy.servername = streamSettings.tlsSettings.serverName;
      }
      if (streamSettings.tlsSettings.alpn) {
        proxy.alpn = streamSettings.tlsSettings.alpn;
      }
    }
  }

  /**
   * 将协议链接转换为出站配置
   */
  parseProtocolLinkToOutbound(link: string): any {
    try {
      const url = new URL(link);
      const protocol = url.protocol.replace(':', '');

      switch (protocol.toLowerCase()) {
        case 'vless':
          return this.parseVlessLink(link);
        case 'vmess':
          return this.parseVmessLink(link);
        case 'trojan':
          return this.parseTrojanLink(link);
        case 'ss':
          return this.parseShadowsocksLink(link);
        case 'socks5':
        case 'socks':
          return this.parseSocksLink(link);
        case 'http':
        case 'https':
          return this.parseHttpLink(link);
        case 'wireguard':
          return this.parseWireguardLink(link);
        default:
          throw new Error(`不支持的协议: ${protocol}`);
      }
    } catch (error) {
      throw new Error(`解析协议链接失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private parseVlessLink(link: string): any {
    const url = new URL(link);
    const userId = url.username;
    const address = url.hostname;
    const port = parseInt(url.port) || 443;
    const params = new URLSearchParams(url.search);
    const tag = decodeURIComponent(url.hash.substring(1)) || `vless-${address}`;

    const outbound = {
      tag,
      protocol: 'vless',
      settings: {
        vnext: [{
          address,
          port,
          users: [{
            id: userId,
            encryption: 'none',
            flow: params.get('flow') || ''
          }]
        }]
      }
    };

    // 添加传输设置
    const streamSettings = this.parseStreamSettings(params);
    if (Object.keys(streamSettings).length > 0) {
      outbound.streamSettings = streamSettings;
    }

    return outbound;
  }

  private parseVmessLink(link: string): any {
    // VMess链接格式: vmess://base64(json)
    const base64Part = link.replace('vmess://', '');
    const vmessJson = JSON.parse(atob(base64Part));

    const outbound = {
      tag: vmessJson.ps || `vmess-${vmessJson.add}`,
      protocol: 'vmess',
      settings: {
        vnext: [{
          address: vmessJson.add,
          port: parseInt(vmessJson.port),
          users: [{
            id: vmessJson.id,
            security: vmessJson.scy || 'auto'
          }]
        }]
      }
    };

    // 构建传输设置
    const streamSettings: any = {
      network: vmessJson.net || 'tcp'
    };

    if (vmessJson.tls === 'tls') {
      streamSettings.security = 'tls';
      streamSettings.tlsSettings = {
        serverName: vmessJson.sni || vmessJson.host || '',
        allowInsecure: false
      };
    }

    // 根据网络类型添加设置
    if (vmessJson.net === 'ws') {
      streamSettings.wsSettings = {
        path: vmessJson.path || '/',
        host: vmessJson.host || ''
      };
    } else if (vmessJson.net === 'grpc') {
      streamSettings.grpcSettings = {
        serviceName: vmessJson.path || 'name'
      };
    } else if (vmessJson.net === 'tcp' && vmessJson.type === 'http') {
      streamSettings.tcpSettings = {
        header: {
          type: 'http',
          request: {
            path: [vmessJson.path || '/'],
            headers: {
              Host: [vmessJson.host || '']
            }
          }
        }
      };
    }

    outbound.streamSettings = streamSettings;
    return outbound;
  }

  private parseTrojanLink(link: string): any {
    const url = new URL(link);
    const password = decodeURIComponent(url.username);
    const address = url.hostname;
    const port = parseInt(url.port) || 443;
    const params = new URLSearchParams(url.search);
    const tag = decodeURIComponent(url.hash.substring(1)) || `trojan-${address}`;

    const outbound = {
      tag,
      protocol: 'trojan',
      settings: {
        servers: [{
          address,
          port,
          password
        }]
      }
    };

    // 添加传输设置
    const streamSettings = this.parseStreamSettings(params);
    if (Object.keys(streamSettings).length > 0) {
      outbound.streamSettings = streamSettings;
    }

    return outbound;
  }

  private parseShadowsocksLink(link: string): any {
    const url = new URL(link);
    const tag = decodeURIComponent(url.hash.substring(1)) || `ss-${url.hostname}`;

    // 解码用户信息
    const userInfo = atob(url.username);
    let method: string;
    let password: string;

    if (userInfo.includes(':')) {
      const parts = userInfo.split(':');
      if (parts.length === 2) {
        // 传统格式: method:password
        method = parts[0];
        password = parts[1];
      } else if (parts.length === 3) {
        // 2022格式: method:serverPassword:userPassword
        method = parts[0];
        password = parts[2]; // 使用用户密码
      } else {
        throw new Error('无效的Shadowsocks链接格式');
      }
    } else {
      throw new Error('无效的Shadowsocks链接格式');
    }

    return {
      tag,
      protocol: 'shadowsocks',
      settings: {
        servers: [{
          address: url.hostname,
          port: parseInt(url.port) || 8388,
          method,
          password
        }]
      }
    };
  }

  private parseSocksLink(link: string): any {
    const url = new URL(link);
    const tag = decodeURIComponent(url.hash.substring(1)) || `socks-${url.hostname}`;

    const outbound = {
      tag,
      protocol: 'socks',
      settings: {
        servers: [{
          address: url.hostname,
          port: parseInt(url.port) || 1080
        }]
      }
    };

    // 如果有用户名和密码
    if (url.username && url.password) {
      outbound.settings.servers[0].users = [{
        user: decodeURIComponent(url.username),
        pass: decodeURIComponent(url.password)
      }];
    }

    return outbound;
  }

  private parseHttpLink(link: string): any {
    const url = new URL(link);
    const tag = decodeURIComponent(url.hash.substring(1)) || `http-${url.hostname}`;

    const outbound = {
      tag,
      protocol: 'http',
      settings: {
        servers: [{
          address: url.hostname,
          port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 3128)
        }]
      }
    };

    // 如果有用户名和密码
    if (url.username && url.password) {
      outbound.settings.servers[0].users = [{
        user: decodeURIComponent(url.username),
        pass: decodeURIComponent(url.password)
      }];
    }

    return outbound;
  }

  private parseWireguardLink(link: string): any {
    const url = new URL(link);
    const params = new URLSearchParams(url.search);
    const tag = decodeURIComponent(url.hash.substring(1)) || `wg-${url.hostname}`;

    return {
      tag,
      protocol: 'wireguard',
      settings: {
        secretKey: params.get('privatekey') || '',
        peers: [{
          endpoint: `${url.hostname}:${url.port}`,
          publicKey: params.get('publickey') || '',
          allowedIPs: params.get('address')?.split(',') || ['0.0.0.0/0']
        }],
        mtu: parseInt(params.get('mtu') || '1420')
      }
    };
  }

  private parseStreamSettings(params: URLSearchParams): any {
    const streamSettings: any = {};
    const network = params.get('type') || 'tcp';
    const security = params.get('security') || 'none';

    streamSettings.network = network;

    if (security !== 'none') {
      streamSettings.security = security;

      if (security === 'tls') {
        streamSettings.tlsSettings = {
          serverName: params.get('sni') || '',
          allowInsecure: params.get('allowInsecure') === '1'
        };

        const alpn = params.get('alpn');
        if (alpn) {
          streamSettings.tlsSettings.alpn = alpn.split(',');
        }
      } else if (security === 'reality') {
        streamSettings.realitySettings = {
          serverName: params.get('sni') || '',
          fingerprint: params.get('fp') || 'chrome',
          shortId: params.get('sid') || '',
          spiderX: params.get('spx') || '',
          password: params.get('pbk') || ''
        };
      }
    }

    // 根据网络类型添加设置
    switch (network) {
      case 'ws':
        streamSettings.wsSettings = {
          path: params.get('path') || '/',
          host: params.get('host') || ''
        };
        break;
      case 'grpc':
        streamSettings.grpcSettings = {
          serviceName: params.get('serviceName') || params.get('path') || 'name'
        };
        break;
      case 'tcp':
        const headerType = params.get('headerType');
        if (headerType === 'http') {
          streamSettings.tcpSettings = {
            header: {
              type: 'http',
              request: {
                path: [params.get('path') || '/'],
                headers: {
                  Host: [params.get('host') || '']
                }
              }
            }
          };
        }
        break;
      case 'kcp':
        streamSettings.kcpSettings = {
          mtu: parseInt(params.get('mtu') || '1350'),
          tti: parseInt(params.get('tti') || '20'),
          uplinkCapacity: parseInt(params.get('uplinkCapacity') || '5'),
          downlinkCapacity: parseInt(params.get('downlinkCapacity') || '20'),
          congestion: params.get('congestion') === '1',
          readBufferSize: parseInt(params.get('readBufferSize') || '1'),
          writeBufferSize: parseInt(params.get('writeBufferSize') || '1'),
          seed: params.get('seed') || 'Password',
          header: {
            type: params.get('headerType') || 'none'
          }
        };
        break;
    }

    return streamSettings;
  }
}






