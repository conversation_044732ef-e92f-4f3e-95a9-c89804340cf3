import { Group } from './types';

/**
 * 获取分组的显示名称
 * 对于特殊的'all'分组，使用翻译键；其他分组直接返回名称
 */
export function getGroupDisplayName(group: Group, t: (key: string) => string): string {
  if (group.id === 'all') {
    return t('groups.all');
  }
  return group.name;
}

/**
 * 检查是否为全部分组
 */
export function isAllGroup(groupId: string): boolean {
  return groupId === 'all';
}

/**
 * 检查分组是否可以编辑（重命名/删除）
 */
export function isGroupEditable(groupId: string): boolean {
  return !isAllGroup(groupId);
}
