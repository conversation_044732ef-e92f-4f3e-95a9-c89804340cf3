import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 顶部标题 */}
      <View style={styles.header}>
        <Text className="text-2xl font-bold">{t('navigation.settings')}</Text>
      </View>

      {/* 设置内容 */}
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text className="text-lg text-muted-foreground text-center">
            设置页面 - 即将推出
          </Text>
          <Text className="text-sm text-muted-foreground text-center mt-2">
            这里将包含应用的各种设置选项
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    // 删除底部横线
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
});
