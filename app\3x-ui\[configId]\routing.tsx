import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';

export default function RoutingScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: textColor }]}>
          路由规则
        </Text>
        <Text style={[styles.subtitle, { color: textColor + '80' }]}>
          即将推出
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
});
