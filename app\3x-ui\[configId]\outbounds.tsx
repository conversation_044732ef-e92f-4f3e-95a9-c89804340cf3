import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { OutboundConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { smartFetch } from '@/lib/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus } from 'lucide-react-native';
import React, { useCallback, useRef, useMemo, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View, Alert, TouchableOpacity } from 'react-native';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

export default function OutboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig, setServerConfig } = useAppStore();

  // 底部弹出菜单状态
  const actionSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['15%'], []);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;

  // 从serverConfig获取出站列表
  const serverConfig = getServerConfig(configId || '');
  const outbounds = serverConfig?.xray?.outbounds || [];

  // 渲染背景
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 加载出站列表
  const loadOutbounds = useCallback(async () => {
    if (!currentConfig) return;

    try {
      await getThreeXUIXrayConfig(currentConfig);
      // 数据已经在getThreeXUIXrayConfig中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load outbounds failed:', error);
      Alert.alert('错误', '加载出站列表失败');
    }
  }, [currentConfig]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadOutbounds();
    }, [loadOutbounds])
  );

  // 处理卡片点击
  const handleCardPress = (index: number) => {
    setSelectedIndex(index);
    actionSheetRef.current?.present();
  }

  // 处理编辑
  const handleEdit = () => {
    if (selectedIndex >= 0) {
      actionSheetRef.current?.dismiss();
      router.push({
        pathname: '/3x-ui/outbound-config',
        params: {
          configId,
          index: selectedIndex.toString()
        }
      });
    }
  }

  // 处理删除确认
  const handleDeleteConfirm = () => {
    Alert.alert(
      '确认删除',
      '确定要删除此出站配置吗？此操作不可撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: handleDelete
        }
      ]
    );
  }

  // 处理删除配置
  const handleDelete = async () => {
    if (!currentConfig || selectedIndex < 0) return;

    try {
      // 构造移除当前出站配置后的xray配置
      const updatedOutbounds = [...outbounds];
      updatedOutbounds.splice(selectedIndex, 1);

      // 组装完整的 xraySetting
      const xraySettingObj = {
        ...(serverConfig?.xray || {}),
        outbounds: updatedOutbounds,
      };

      const formData = new FormData();
      formData.append('xraySetting', JSON.stringify(xraySettingObj));

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/xray/update`,
        { method: 'POST', body: formData },
        currentConfig
      );
      
      const result = await response.json();
      if (result.success) {
        // 更新本地缓存的 xray 配置
        await getThreeXUIXrayConfig(currentConfig);
        Alert.alert('成功', '出站配置已删除');
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert('错误', result.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete outbound config failed:', error);
      Alert.alert('错误', '删除失败');
    }
  }

  // 渲染出站配置卡片
  const renderOutboundCard = (outbound: OutboundConfig, index: number) => {
    const getProtocolColor = (protocol: string) => {
      switch (protocol) {
        case 'freedom': return 'green';
        case 'blackhole': return 'red';
        case 'dns': return 'blue';
        case 'http': return 'orange';
        case 'socks': return 'purple';
        case 'shadowsocks': return 'gray';
        case 'trojan': return 'pink';
        case 'vless': return 'cyan';
        case 'vmess': return 'indigo';
        case 'wireguard': return 'yellow';
        default: return 'gray';
      }
    };



    return (
      <View key={index} style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => handleCardPress(index)}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.title, { color: textColor }]}>
              {outbound.tag || `出站-${index + 1}`}
            </Text>
            <View style={styles.rightSection}>
              <Badge color={getProtocolColor(outbound.protocol)}>
                <Text style={styles.badgeText}>{outbound.protocol.toUpperCase()}</Text>
              </Badge>
            </View>
          </View>
        </TouchableOpacity>
        <View style={[styles.divider, { backgroundColor: borderColor }]} />
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 固定在顶部的按钮组 */}
      <View style={[styles.headerContainer, { backgroundColor }]}>
        <View style={styles.header}>
          <View style={styles.buttonGroup}>
            <Button
              variant="secondary"
              size="sm"
              onPress={() => {
                router.push({
                  pathname: '/3x-ui/outbound-config',
                  params: { configId }
                });
              }}
              style={styles.button}
            >
              <Plus size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>添加</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={() => {
                // TODO: 实现WARP功能
                Alert.alert('提示', 'WARP功能即将推出');
              }}
              style={styles.button}
            >
              <Text style={[styles.buttonText, { color: textColor }]}>WARP</Text>
            </Button>



            <Button
              variant="secondary"
              size="sm"
              onPress={() => {
                // TODO: 实现重启功能
                Alert.alert('提示', '重启功能即将推出');
              }}
              style={styles.button}
            >
              <Text style={[styles.buttonText, { color: textColor }]}>重启</Text>
            </Button>
          </View>
        </View>
        <View style={[styles.headerDivider, { backgroundColor: borderColor }]} />
      </View>

      {/* 可滚动的内容区域 */}
      <ScrollView style={styles.scrollView}>

        <View style={styles.content}>
          {outbounds.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: textColor + '60' }]}>
                暂无出站配置
              </Text>
              <Text style={[styles.emptySubtext, { color: textColor + '40' }]}>
                点击右上角添加按钮创建新的出站配置
              </Text>
            </View>
          ) : (
            outbounds.map((outbound: OutboundConfig, index: number) => renderOutboundCard(outbound, index))
          )}
        </View>
      </ScrollView>

      {/* 操作底部弹窗 */}
      <BottomSheetModal
        ref={actionSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[actionSheetStyles.content, { backgroundColor }]}>
          <View style={actionSheetStyles.buttonsContainer}>
            <Button
              variant="ghost"
              onPress={handleEdit}
            >
              <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>
                编辑配置
              </Text>
            </Button>

            <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant="ghost"
              onPress={handleDeleteConfirm}
            >
              <Text style={[actionSheetStyles.buttonText, actionSheetStyles.deleteButtonText]}>
                删除配置
              </Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    // 固定在顶部的容器
  },
  header: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    paddingTop: 0,
  },
  cardContainer: {
    // 不需要额外的margin，由padding控制间距
  },
  card: {
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  headerDivider: {
    height: 1,
  },
  divider: {
    height: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

const actionSheetStyles = StyleSheet.create({
  content: {
    padding: 0,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});
